# Test script to verify agent forms work correctly
require 'net/http'
require 'uri'

puts "Testing agent forms..."

# First, ensure we have a user and tenant
user = User.first
if user.nil?
  puts "No user found. Please create a user first."
  exit
end

tenant = user.current_tenant || user.tenants.first
if tenant.nil?
  puts "No tenant found. Creating a test tenant..."
  tenant = user.tenants.create!(name: "Test Tenant")
  user.update!(current_tenant: tenant)
end

puts "User: #{user.email}"
puts "Tenant: #{tenant.name}"

# Set the tenant context
Current.tenant = tenant
ActsAsTenant.current_tenant = tenant

# Test creating an agent with specializations
puts "\nTesting agent creation with specializations..."
agent = tenant.agents.build(
  name: "Test Sales Agent",
  kind: "sales",
  description: "A test sales agent",
  greeting_message: "Hello! How can I help you today?",
  personality: "Friendly and helpful",
  response_style: "professional",
  language: "en",
  llm_provider: "openai",
  model_name: "gpt-4",
  temperature: 0.7,
  max_tokens: 1000,
  system_prompt: "You are a helpful sales assistant."
)

# Add specializations
agent.agent_specializations.build(
  name: "Product Recommendations",
  description: "Expert at recommending products"
)

agent.agent_specializations.build(
  name: "Pricing Inquiries", 
  description: "Can answer pricing and discount questions"
)

if agent.save
  puts "✓ Agent created successfully!"
  puts "  ID: #{agent.id}"
  puts "  Name: #{agent.name}"
  puts "  Kind: #{agent.kind}"
  puts "  Specializations: #{agent.agent_specializations.count}"
  agent.agent_specializations.each do |spec|
    puts "    - #{spec.name}: #{spec.description}"
  end
else
  puts "✗ Failed to create agent:"
  agent.errors.full_messages.each do |msg|
    puts "  - #{msg}"
  end
end

puts "\nTest complete."