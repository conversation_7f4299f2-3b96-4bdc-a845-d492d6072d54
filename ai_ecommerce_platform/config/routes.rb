Rails.application.routes.draw do
  devise_for :users, controllers: {
    registrations: 'users/registrations',
    sessions: 'users/sessions'
  }
  
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root "landing#index"
  
  authenticated :user do
    get 'dashboard', to: 'dashboard#index', as: :user_root
    get 'analytics', to: 'dashboard#analytics', as: :analytics_dashboard
    get 'theme-test', to: 'dashboard#theme_test', as: :theme_test_dashboard
  end
  
  resources :conversations, only: [:index, :show, :create] do
    resources :messages, only: [:create]
  end
  
  resources :tenants, only: [:new, :create]
  
  resources :agents do
    member do
      post :test
      get :conversations
    end
  end
  
  resources :knowledge_sources do
    member do
      post :process_document
    end
  end
  
  resources :integrations do
    collection do
      get :widget
      patch :update_widget_settings
    end
  end
  
  namespace :admin do
    root 'dashboard#index'
    resources :tenants
    resources :users
    resources :agents
    resources :conversations, only: [:index, :show]
    resources :integrations
    resources :knowledge_sources
  end
  
  # API endpoints
  namespace :api do
    namespace :v1 do
      post 'chat', to: 'chat#create'
      get 'chat/:session_id', to: 'chat#show'
      get 'chat/:session_id/messages', to: 'chat#messages'
      
      get 'widget/config', to: 'widget#config'
      post 'widget/events', to: 'widget#events'
    end
  end
  
  # Webhook endpoints
  post 'webhooks/:platform/:token', to: 'webhooks#handle', as: :webhook
  
  # Mount ActionCable
  mount ActionCable.server => '/cable'
end
