<!DOCTYPE html>
<html class="<%= cookies[:theme] == 'dark' ? 'dark' : '' %>">
  <head>
    <title><%= content_for(:title) || "AI Dashboard" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
      <!-- Sidebar Navigation for Agent Conversations -->
      <aside class="fixed top-0 left-0 w-56 h-screen bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto z-30 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="py-4">
          <!-- Logo/Brand -->
          <div class="px-4 mb-6">
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">AI Platform</h1>
            <p class="text-sm text-gray-500 dark:text-gray-400">Agent Conversations</p>
          </div>

          <!-- Navigation Header -->
          <h2 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
            Navigation
          </h2>

          <!-- Main Navigation Items -->
          <nav class="space-y-1 px-2">
            <%= link_to user_root_path, class: "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg #{current_page?(user_root_path) ? 'bg-teal-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'} transition-colors" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
              Dashboard
            <% end %>

            <%= link_to agents_path, class: "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg #{request.path.include?('/agents') ? 'bg-teal-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'} transition-colors" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              Multi-Agent Hub
            <% end %>

            <%= link_to analytics_dashboard_path, class: "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg #{current_page?(analytics_dashboard_path) ? 'bg-teal-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'} transition-colors" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Analytics
            <% end %>
          </nav>

          <!-- Agents Section -->
          <% if defined?(@agents) && @agents.present? %>
            <div class="mt-8">
              <h2 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-4">
                Agents
              </h2>
              <nav class="space-y-1 px-2">
                <% @agents.each do |agent| %>
                  <%= link_to conversations_agent_path(agent), class: "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg #{defined?(@agent) && @agent&.id == agent.id ? 'bg-teal-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'} transition-colors" do %>
                    <div class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium" style="background-color: <%= agent.kind == 'customer_service' ? '#32b8c6' : agent.kind == 'sales' ? '#a84b2f' : '#5e5240' %>; color: white;">
                      <%= agent.kind == 'customer_service' ? '💬' : agent.kind == 'sales' ? '💰' : '🔧' %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="font-medium truncate"><%= agent.name %></div>
                      <div class="text-xs text-gray-500 dark:text-gray-400 truncate"><%= agent.kind.humanize %></div>
                    </div>
                  <% end %>
                <% end %>
              </nav>
            </div>
          <% end %>
        </div>
      </aside>

      <!-- Main Content Area -->
      <main class="lg:ml-56 min-h-screen">
        <!-- Mobile Header -->
        <div class="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
          <h1 class="text-lg font-semibold text-gray-900 dark:text-white">Agent Conversations</h1>
          <button id="mobileSidebarToggle" class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>

        <%= yield %>
      </main>

      <!-- Mobile Sidebar Overlay -->
      <div class="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden hidden" id="sidebarOverlay"></div>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script>
      // Initialize agent conversation layout interactions
      document.addEventListener('DOMContentLoaded', function() {
        const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        // Mobile sidebar toggle
        if (mobileSidebarToggle) {
          mobileSidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('-translate-x-full');
            sidebarOverlay.classList.toggle('hidden');
          });
        }

        // Close sidebar when clicking overlay
        if (sidebarOverlay) {
          sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.add('-translate-x-full');
            sidebarOverlay.classList.add('hidden');
          });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
          if (window.innerWidth >= 1024) {
            // Desktop: ensure sidebar is visible and overlay is hidden
            sidebar.classList.remove('-translate-x-full');
            sidebarOverlay.classList.add('hidden');
          } else {
            // Mobile: ensure sidebar is hidden by default
            sidebar.classList.add('-translate-x-full');
          }
        });

        // Tab switching (if tabs exist)
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Update active states
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(t => t.classList.remove('active'));

            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + 'Tab');
            if (targetContent) {
              targetContent.classList.add('active');
            }
          });
        });
      });
    </script>
  </body>
</html>