<!-- Top Navigation Bar -->
<nav class="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-6 fixed top-0 left-0 right-0 z-50" data-controller="theme-toggle">
  <div class="flex items-center justify-between w-full">
    <!-- Left Side -->
    <div class="flex items-center gap-6">
      <!-- Logo/Brand -->
      <div class="flex items-center gap-3">
        <div class="text-2xl font-bold text-gray-900 dark:text-white">AI Agent System</div>
        <span class="px-2 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded">
          All systems operational
        </span>
      </div>
    </div>

    <!-- Center - Search -->
    <div class="flex-1 max-w-2xl mx-8">
      <div class="relative">
        <input type="text" 
               placeholder="Search agents, workflows, or settings..." 
               class="w-full px-4 py-2 pl-10 text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Right Side -->
    <div class="flex items-center gap-4">
      <!-- Notifications -->
      <button class="relative p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
        </svg>
        <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
      </button>

      <!-- Theme Toggle -->
      <button id="theme-toggle" class="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" data-theme-toggle-target="button" data-action="click->theme-toggle#toggle">
        <span data-theme-toggle-target="icon">
          <!-- Icon will be dynamically updated by the controller -->
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
          </svg>
        </span>
      </button>

      <!-- Settings -->
      <button class="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
      </button>

      <!-- User Profile -->
      <div class="flex items-center gap-3 pl-4 border-l border-gray-200 dark:border-gray-700">
        <div class="text-right">
          <p class="text-sm font-medium text-gray-900 dark:text-white">Admin User</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">System Administrator</p>
        </div>
        <button class="w-10 h-10 bg-teal-600 text-white rounded-full flex items-center justify-center hover:ring-2 hover:ring-teal-500 hover:ring-offset-2 dark:hover:ring-offset-gray-800 transition-all">
          <span class="text-sm font-semibold">AD</span>
        </button>
      </div>
    </div>
  </div>
</nav>

<!-- Spacer for fixed navbar -->
<div class="h-16"></div>