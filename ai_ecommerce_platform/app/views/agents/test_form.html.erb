<h1>Test Form</h1>

<% begin %>
  <p>Testing agent object: <%= @agent.inspect %></p>
  <p>Agent class: <%= @agent.class %></p>
  <p>Agent persisted?: <%= @agent.persisted? %></p>
  
  <h2>Test 1: Simple form</h2>
  <%= form_with(url: agents_path, method: :post, local: true) do |form| %>
    <p>Simple form works!</p>
  <% end %>
  
  <h2>Test 2: Form with model (no route)</h2>
  <%= form_with(model: @agent, skip_default_ids: true, url: agents_path, local: true) do |form| %>
    <p>Form with model works!</p>
  <% end %>
  
  <h2>Test 3: Form fields</h2>
  <%= form_with(url: agents_path, local: true) do |form| %>
    <%= form.text_field :name, value: @agent.name %>
    <p>Form fields work!</p>
  <% end %>
  
<% rescue => e %>
  <div style="background: red; color: white; padding: 20px;">
    <h3>Error: <%= e.class %></h3>
    <p><%= e.message %></p>
    <pre><%= e.backtrace.first(5).join("\n") %></pre>
  </div>
<% end %>