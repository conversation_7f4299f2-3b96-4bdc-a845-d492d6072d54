<% content_for :title, "Create New Agent" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create New Agent</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure your AI agent's personality, behavior, and capabilities</p>
        </div>
        <div class="flex items-center gap-3">
          <%= link_to agents_path, class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to Agents
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">
    <div class="max-w-4xl mx-auto">
      <% if @agent %>
        <% Rails.logger.debug "Agent in view: #{@agent.inspect}" %>
        <% Rails.logger.debug "Agent class: #{@agent.class}" %>
        <%= render 'form', agent: @agent %>
      <% else %>
        <!-- Error message when @agent is nil -->
        <div class="rounded-lg bg-red-50 dark:bg-red-900/30 p-6 border border-red-200 dark:border-red-800">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Unable to Load Agent Form
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>There was an issue setting up the agent creation form. Please try refreshing the page or contact support if the problem persists.</p>
              </div>
              <div class="mt-4">
                <%= link_to agents_path, class: "inline-flex items-center px-3 py-2 text-sm font-medium text-red-800 dark:text-red-200 bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/70 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                  </svg>
                  Back to Agents
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>