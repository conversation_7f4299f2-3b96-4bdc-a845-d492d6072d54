<% Rails.logger.debug "=== FORM DEBUG ===" %>
<% Rails.logger.debug "agent param: #{agent.inspect}" %>
<% Rails.logger.debug "agent class: #{agent.class}" %>
<% Rails.logger.debug "agent.is_a?(Agent): #{agent.is_a?(Agent)}" %>
<% Rails.logger.debug "local_assigns: #{local_assigns.inspect}" %>

<%= form_with(model: agent, local: true, data: { turbo: false }) do |form| %>
  <% if agent.errors.any? %>
    <div class="rounded-lg bg-red-50 dark:bg-red-900/30 p-4 mb-6 border border-red-200 dark:border-red-800">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(agent.errors.count, "error") %> with your submission
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% agent.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Basic Information Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Basic Information</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Configure your agent's basic details and settings</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_field :name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "e.g., Emma - Sales Assistant" %>
      </div>

      <div>
        <%= form.label :kind, "Agent Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.select :kind, options_for_select(Agent::AGENT_KINDS.map { |k| [k.humanize, k] }, agent.kind), { prompt: 'Select agent type' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
      </div>

      <div class="md:col-span-2">
        <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_area :description, rows: 3, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "Describe what this agent does and its primary responsibilities" %>
      </div>
    </div>
  </div>

  <!-- Personality & Behavior Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personality & Behavior</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Define how your agent communicates and behaves</p>
    </div>

    <div class="space-y-6">
      <div>
        <%= form.label :greeting_message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_area :greeting_message, rows: 3, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "Hello! I'm Emma, your sales assistant. How can I help you today?" %>
      </div>

      <div>
        <%= form.label :personality, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_area :personality, rows: 4, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "Friendly, professional, and knowledgeable about our products. Always eager to help and provides detailed explanations." %>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <%= form.label :response_style, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.select :response_style, [
            ['Professional', 'professional'],
            ['Friendly', 'friendly'],
            ['Casual', 'casual'],
            ['Technical', 'technical']
          ], { prompt: 'Select style' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
        </div>

        <div>
          <%= form.label :language, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.select :language, [
            ['English', 'en'],
            ['Spanish', 'es'],
            ['French', 'fr'],
            ['German', 'de'],
            ['Italian', 'it'],
            ['Auto-detect', 'auto']
          ], { selected: 'en' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
        </div>
      </div>
    </div>
  </div>

  <!-- AI Model Configuration Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI Model Configuration</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Configure the AI model settings and behavior parameters</p>
    </div>

    <div class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <%= form.label :llm_provider, "LLM Provider", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.select :llm_provider, [
            ['OpenAI', 'openai'],
            ['Anthropic', 'anthropic'],
            ['Google', 'google'],
            ['DeepSeek', 'deepseek'],
            ['OpenRouter', 'openrouter'],
            ['Ollama', 'ollama'],
            ['AWS Bedrock', 'bedrock']
          ], { prompt: 'Select provider' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
        </div>

        <div>
          <%= form.label :model_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.text_field :model_name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "gpt-4" %>
        </div>

        <div>
          <%= form.label :temperature, "Temperature (0-2)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.number_field :temperature, step: 0.1, min: 0, max: 2, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", value: agent.temperature || 0.7 %>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Lower values make responses more focused and deterministic</p>
        </div>

        <div>
          <%= form.label :max_tokens, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.number_field :max_tokens, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", value: agent.max_tokens || 1000 %>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Maximum length of responses</p>
        </div>
      </div>

      <div>
        <%= form.label :system_prompt, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_area :system_prompt, rows: 5, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "You are a helpful sales assistant for an e-commerce store. You have deep knowledge about our products and can help customers find what they need..." %>
      </div>
    </div>
  </div>

  <!-- Specializations Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Specializations</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Define what this agent specializes in</p>
    </div>

    <div data-controller="nested-form">
      <div data-nested-form-target="items">
        <%= form.fields_for :agent_specializations do |spec_form| %>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600" data-nested-form-target="item">
            <div>
              <%= spec_form.label :name, "Specialization", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= spec_form.text_field :name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "e.g., Product recommendations" %>
            </div>
            <div>
              <%= spec_form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= spec_form.text_field :description, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "Brief description" %>
            </div>
            <div class="flex items-end">
              <%= spec_form.check_box :_destroy, class: "hidden" %>
              <button type="button" data-action="click->nested-form#remove" class="w-full px-3 py-2 text-sm font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
                Remove
              </button>
            </div>
          </div>
        <% end %>
      </div>

      <button type="button" data-action="click->nested-form#add" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Add Specialization
      </button>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex items-center justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
    <%= link_to "Cancel", agents_path, class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
    <%= form.submit class: "px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
  </div>
<% end %>