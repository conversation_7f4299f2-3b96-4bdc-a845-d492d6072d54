<div class="debug-info bg-yellow-100 p-4 mb-4">
  <h3>Debug Info:</h3>
  <p>Agent class: <%= agent.class.name %></p>
  <p>Agent ID: <%= agent.id || 'nil' %></p>
  <p>Agent persisted?: <%= agent.persisted? %></p>
  <p>Agent name: <%= agent.name.inspect %></p>
  <p>Agent kind: <%= agent.kind.inspect %></p>
  <p>Current tenant: <%= current_tenant&.name %></p>
</div>

<%= form_with(model: [:admin, agent], local: true) do |form| %>
  <div class="bg-white p-4 rounded">
    <div class="mb-4">
      <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
      <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-md" %>
    </div>
    
    <div class="mb-4">
      <%= form.label :kind, class: "block text-sm font-medium text-gray-700 mb-2" %>
      <%= form.select :kind, options_for_select(Agent::AGENT_KINDS.map { |k| [k.humanize, k] }, agent.kind), 
                      { prompt: 'Select agent type' }, 
                      class: "w-full px-3 py-2 border border-gray-300 rounded-md" %>
    </div>
    
    <%= form.submit "Save Agent", class: "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" %>
  </div>
<% end %>