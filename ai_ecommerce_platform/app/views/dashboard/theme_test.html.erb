<% content_for :title, "Theme Test - AI Dashboard" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
      <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Theme System Test</h1>
      <p class="text-gray-600 dark:text-gray-400">Test the light/dark theme switching functionality and persistence</p>
    </div>

    <!-- Theme Controls -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Theme Controls</h2>
      
      <div class="flex items-center gap-4 mb-4">
        <button id="set-light" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
          Set Light Theme
        </button>
        <button id="set-dark" class="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors">
          Set Dark Theme
        </button>
        <button id="toggle-theme" class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">
          Toggle Theme
        </button>
      </div>
      
      <div class="text-sm text-gray-600 dark:text-gray-400">
        <p><strong>Current Theme:</strong> <span id="current-theme">Loading...</span></p>
        <p><strong>Cookie Value:</strong> <span id="cookie-value">Loading...</span></p>
        <p><strong>HTML Class:</strong> <span id="html-class">Loading...</span></p>
      </div>
    </div>

    <!-- Visual Test Elements -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- Form Elements Test -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Form Elements</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Text Input</label>
            <input type="text" placeholder="Enter some text..." class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select</label>
            <select class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Textarea</label>
            <textarea rows="3" placeholder="Enter a message..." class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"></textarea>
          </div>
        </div>
      </div>

      <!-- Interactive Elements Test -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Interactive Elements</h3>
        
        <div class="space-y-4">
          <div class="flex gap-2">
            <button class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">Primary Button</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">Secondary Button</button>
          </div>
          
          <div class="flex items-center gap-4">
            <label class="flex items-center">
              <input type="checkbox" class="h-4 w-4 text-teal-600 border-gray-300 dark:border-gray-600 rounded focus:ring-teal-500">
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Checkbox</span>
            </label>
            
            <label class="flex items-center">
              <input type="radio" name="radio-test" class="h-4 w-4 text-teal-600 border-gray-300 dark:border-gray-600 focus:ring-teal-500">
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Radio</span>
            </label>
          </div>
          
          <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
            <p class="text-sm text-gray-600 dark:text-gray-400">This is a test card with hover effects.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Test -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Navigation Test</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">Test theme persistence across page navigation:</p>
      
      <div class="flex gap-4">
        <%= link_to "Dashboard", user_root_path, class: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" %>
        <%= link_to "Agents", agents_path, class: "px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors" %>
        <%= link_to "Analytics", analytics_dashboard_path, class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors" %>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Update theme status display
  function updateThemeStatus() {
    const isDark = document.documentElement.classList.contains('dark');
    const cookieValue = getCookie('theme') || 'not set';
    
    document.getElementById('current-theme').textContent = isDark ? 'Dark' : 'Light';
    document.getElementById('cookie-value').textContent = cookieValue;
    document.getElementById('html-class').textContent = isDark ? 'dark' : 'light';
  }
  
  // Get cookie helper
  function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
  }
  
  // Set theme helper
  function setTheme(theme) {
    const html = document.documentElement;
    
    if (theme === 'dark') {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }
    
    document.cookie = `theme=${theme}; path=/; max-age=31536000; SameSite=Lax`;
    updateThemeStatus();
    
    // Dispatch event for other controllers
    document.dispatchEvent(new CustomEvent('theme:changed', { 
      detail: { theme } 
    }));
  }
  
  // Event listeners
  document.getElementById('set-light').addEventListener('click', () => setTheme('light'));
  document.getElementById('set-dark').addEventListener('click', () => setTheme('dark'));
  document.getElementById('toggle-theme').addEventListener('click', () => {
    const isDark = document.documentElement.classList.contains('dark');
    setTheme(isDark ? 'light' : 'dark');
  });
  
  // Initial status update
  updateThemeStatus();
  
  // Update status when theme changes
  document.addEventListener('theme:changed', updateThemeStatus);
});
</script>
