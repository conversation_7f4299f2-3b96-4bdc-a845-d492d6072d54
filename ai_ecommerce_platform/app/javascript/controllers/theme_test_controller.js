import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') return
    
    console.log('🎨 Theme Test Controller: Starting theme persistence tests...')
    
    // Test 1: Check initial theme state
    this.testInitialThemeState()
    
    // Test 2: Test theme persistence across page loads
    this.testThemePersistence()
    
    // Test 3: Test cookie synchronization
    this.testCookieSynchronization()
    
    // Test 4: Test theme toggle functionality
    this.testThemeToggle()
    
    console.log('✅ Theme Test Controller: All tests completed')
  }
  
  testInitialThemeState() {
    const html = document.documentElement
    const cookieTheme = this.getCookie('theme')
    const hasClass = html.classList.contains('dark')
    
    console.log('📋 Test 1: Initial Theme State')
    console.log(`  Cookie value: ${cookieTheme || 'not set'}`)
    console.log(`  HTML class: ${hasClass ? 'dark' : 'light'}`)
    console.log(`  Match: ${(cookieTheme === 'dark') === hasClass ? '✅' : '❌'}`)
  }
  
  testThemePersistence() {
    console.log('📋 Test 2: Theme Persistence')
    
    // Check if theme persists after simulated page reload
    const currentTheme = this.getCookie('theme') || 'light'
    const expectedClass = currentTheme === 'dark'
    const actualClass = document.documentElement.classList.contains('dark')
    
    console.log(`  Expected: ${expectedClass ? 'dark' : 'light'}`)
    console.log(`  Actual: ${actualClass ? 'dark' : 'light'}`)
    console.log(`  Persistence: ${expectedClass === actualClass ? '✅' : '❌'}`)
  }
  
  testCookieSynchronization() {
    console.log('📋 Test 3: Cookie Synchronization')
    
    const cookie = this.getCookie('theme')
    const htmlClass = document.documentElement.classList.contains('dark')
    
    // Test cookie format
    const cookieValid = cookie === 'dark' || cookie === 'light' || cookie === null
    console.log(`  Cookie format valid: ${cookieValid ? '✅' : '❌'}`)
    
    // Test synchronization
    const synchronized = (cookie === 'dark') === htmlClass
    console.log(`  Cookie-HTML sync: ${synchronized ? '✅' : '❌'}`)
    
    // Test cookie attributes (should have path and max-age)
    const cookieString = document.cookie
    const hasPath = cookieString.includes('path=/')
    const hasMaxAge = cookieString.includes('max-age=')
    
    console.log(`  Cookie has path: ${hasPath ? '✅' : '❌'}`)
    console.log(`  Cookie has max-age: ${hasMaxAge ? '✅' : '❌'}`)
  }
  
  testThemeToggle() {
    console.log('📋 Test 4: Theme Toggle Functionality')
    
    // Find theme toggle buttons
    const toggleButtons = document.querySelectorAll('[data-theme-toggle-target="button"]')
    const iconElements = document.querySelectorAll('[data-theme-toggle-target="icon"]')
    
    console.log(`  Toggle buttons found: ${toggleButtons.length}`)
    console.log(`  Icon elements found: ${iconElements.length}`)
    
    // Test if buttons are properly connected
    const buttonsWorking = toggleButtons.length > 0
    const iconsWorking = iconElements.length > 0
    
    console.log(`  Buttons functional: ${buttonsWorking ? '✅' : '❌'}`)
    console.log(`  Icons functional: ${iconsWorking ? '✅' : '❌'}`)
    
    // Test icon content based on current theme
    if (iconElements.length > 0) {
      const isDark = document.documentElement.classList.contains('dark')
      const firstIcon = iconElements[0]
      const iconContent = firstIcon.innerHTML
      
      // Dark mode should show sun icon, light mode should show moon icon
      const hasSunIcon = iconContent.includes('M12 3v1m0 16v1m9-9h-1M4 12H3')
      const hasMoonIcon = iconContent.includes('M20.354 15.354A9 9 0 018.646 3.646')
      
      const correctIcon = isDark ? hasSunIcon : hasMoonIcon
      console.log(`  Correct icon displayed: ${correctIcon ? '✅' : '❌'}`)
      console.log(`  Current theme: ${isDark ? 'dark' : 'light'}`)
      console.log(`  Expected icon: ${isDark ? 'sun' : 'moon'}`)
      console.log(`  Has sun icon: ${hasSunIcon}`)
      console.log(`  Has moon icon: ${hasMoonIcon}`)
    }
  }
  
  getCookie(name) {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  }
}
