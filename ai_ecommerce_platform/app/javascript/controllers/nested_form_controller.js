import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["items", "item"]
  
  connect() {
    console.log("Nested form controller connected")
  }
  
  add(event) {
    event.preventDefault()
    
    // Create a new specialization item
    const timestamp = Date.now()
    const newItem = document.createElement('div')
    newItem.className = "grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
    newItem.setAttribute('data-nested-form-target', 'item')
    newItem.innerHTML = `
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Specialization</label>
        <input type="text" name="agent[agent_specializations_attributes][${timestamp}][name]" 
               class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" 
               placeholder="e.g., Product recommendations">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
        <input type="text" name="agent[agent_specializations_attributes][${timestamp}][description]" 
               class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" 
               placeholder="Brief description">
      </div>
      <div class="flex items-end">
        <button type="button" data-action="click->nested-form#remove" 
                class="w-full px-3 py-2 text-sm font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
          Remove
        </button>
      </div>
    `
    
    this.itemsTarget.appendChild(newItem)
  }
  
  remove(event) {
    event.preventDefault()
    const item = event.target.closest('[data-nested-form-target="item"]')
    
    if (item) {
      // Check if this is an existing record that needs to be marked for destruction
      const destroyInput = item.querySelector('input[name*="_destroy"]')
      if (destroyInput) {
        destroyInput.value = "1"
        item.style.display = "none"
      } else {
        // It's a new record, just remove it
        item.remove()
      }
    }
  }
}
