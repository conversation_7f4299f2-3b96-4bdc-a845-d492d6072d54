import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "icon"]

  connect() {
    // Initialize theme from cookie or default to light
    this.applyTheme()
    this.updateAllIcons()

    // Listen for theme changes from other controllers
    document.addEventListener('theme:changed', this.handleThemeChange.bind(this))
  }

  disconnect() {
    document.removeEventListener('theme:changed', this.handleThemeChange.bind(this))
  }

  toggle() {
    const html = document.documentElement
    const isDark = html.classList.contains('dark')

    const newTheme = isDark ? 'light' : 'dark'
    this.setTheme(newTheme)
  }

  setTheme(theme) {
    const html = document.documentElement

    // Apply theme class
    if (theme === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }

    // Save to cookie with 1 year expiration
    document.cookie = `theme=${theme}; path=/; max-age=31536000; SameSite=Lax`

    // Update all theme toggle icons across the page
    this.updateAllIcons()

    // Dispatch custom event for other controllers
    document.dispatchEvent(new CustomEvent('theme:changed', {
      detail: { theme }
    }))

    // Update chart themes if they exist
    this.updateChartThemes()

    console.log(`Theme switched to: ${theme}`)
  }

  applyTheme() {
    const theme = this.getCookie('theme') || 'light'
    const html = document.documentElement

    if (theme === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }

    // Remove any legacy data-color-scheme attributes
    html.removeAttribute('data-color-scheme')
  }

  updateAllIcons() {
    const isDark = document.documentElement.classList.contains('dark')

    // Update all theme toggle icons on the page
    const allThemeToggles = document.querySelectorAll('[data-theme-toggle-target="icon"], [data-theme-target="icon"]')
    allThemeToggles.forEach(icon => {
      this.updateIconElement(icon, isDark)
    })

    // Update icons in this controller instance
    if (this.hasIconTarget) {
      this.updateIconElement(this.iconTarget, isDark)
    }
  }

  updateIconElement(iconElement, isDark) {
    if (!iconElement) return

    if (isDark) {
      // Show sun icon (to switch to light)
      iconElement.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
          </path>
        </svg>
      `
    } else {
      // Show moon icon (to switch to dark)
      iconElement.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
          </path>
        </svg>
      `
    }
  }

  updateChartThemes() {
    // Update Chart.js themes if they exist
    if (window.performanceChart) {
      const isDark = document.documentElement.classList.contains('dark')
      const gridColor = isDark ? 'rgba(119, 124, 124, 0.2)' : 'rgba(94, 82, 64, 0.1)'

      window.performanceChart.options.scales.y.grid.color = gridColor
      window.performanceChart.options.scales.x.grid.color = gridColor
      window.performanceChart.update()
    }
  }

  handleThemeChange(event) {
    // Sync with theme changes from other controllers
    this.updateAllIcons()
  }

  getCookie(name) {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  }
}