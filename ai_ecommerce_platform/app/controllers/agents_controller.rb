class AgentsController < ApplicationController
  before_action :set_agent, only: [:show, :edit, :update, :destroy, :test, :conversations]

  def index
    ensure_current_tenant!
    @agents = current_tenant.agents.includes(:agent_specializations)
  end

  def show
    @recent_conversations = @agent.conversations
      .includes(:customer, :messages)
      .order(created_at: :desc)
      .limit(10)
    
    @performance_metrics = {
      total_conversations: @agent.conversations.count,
      avg_response_time: calculate_avg_response_time(@agent),
      satisfaction_rate: calculate_satisfaction_rate(@agent),
      resolution_rate: calculate_resolution_rate(@agent)
    }
  end

  def new
    ensure_current_tenant!
    return if performed? # Stop if ensure_current_tenant! redirected

    if current_tenant.nil?
      redirect_to root_path, alert: "Unable to determine current tenant. Please try again."
      return
    end

    @agent = current_tenant.agents.build

    # Ensure agent has required attributes for form
    @agent.name ||= ""
    @agent.kind ||= ""
    @agent.description ||= ""

    @agent.agent_specializations.build
  rescue => e
    Rails.logger.error "Error in agents#new: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    redirect_to root_path, alert: "An error occurred. Please try again."
  end

  def create
    ensure_current_tenant!
    @agent = current_tenant.agents.build(agent_params)

    if @agent.save
      redirect_to @agent, notice: 'Agent was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @agent.agent_specializations.build if @agent.agent_specializations.empty?
  end

  def update
    if @agent.update(agent_params)
      redirect_to @agent, notice: 'Agent was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @agent.destroy!
    redirect_to agents_url, notice: 'Agent was successfully deleted.'
  end

  def test
    test_message = params[:test_message] || "Hello, can you help me?"
    
    response = Ai::TestService.new(@agent).test_response(test_message)
    
    render json: {
      success: true,
      response: response,
      agent_name: @agent.name
    }
  rescue => e
    render json: {
      success: false,
      error: e.message
    }, status: :unprocessable_entity
  end

  def conversations
    @agents = current_tenant.agents
    @active_conversation = @agent.conversations.includes(:messages, :customer).last
    
    # Metrics for the right panel
    @metrics = {
      active_conversations: @agent.conversations.where(status: 'active').count,
      resolved_today: @agent.conversations.where(status: 'resolved', updated_at: Date.current.all_day).count,
      avg_response_time: "1.2s",
      satisfaction_score: 4.8
    }
    
    # Performance data for the chart
    @performance_data = generate_performance_data
    
    render layout: 'agent_conversation'
  end

  private

  def set_agent
    ensure_current_tenant!
    @agent = current_tenant.agents.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to agents_path, alert: "Agent not found."
  end

  def ensure_current_tenant!
    # Check if we already have a current tenant
    return if current_tenant.present?

    # Ensure we have a current user
    unless current_user
      redirect_to new_user_session_path, alert: "Please sign in first"
      return
    end

    # Try to set tenant from user's current_tenant
    if current_user.current_tenant.present?
      Current.tenant = current_user.current_tenant
      ActsAsTenant.current_tenant = current_user.current_tenant
      Rails.logger.info "Set current tenant from user: #{current_user.current_tenant.name}"
      return
    end

    # If user has tenants but no current_tenant set, use the first one
    user_tenants = current_user.tenants
    if user_tenants.any?
      tenant = user_tenants.first
      current_user.update!(current_tenant: tenant)
      Current.tenant = tenant
      ActsAsTenant.current_tenant = tenant
      Rails.logger.info "Set current tenant to first available: #{tenant.name}"
      return
    end

    # No tenant available, redirect to dashboard with instructions
    redirect_to root_path, alert: "Please create or select a tenant first to manage agents"
  end

  def agent_params
    params.require(:agent).permit(
      :name, :kind, :description, :status, :llm_provider,
      :role, :personality, :greeting_message, :avatar_url, :response_style, :language,
      :model_provider, :ai_model_name, :temperature, :max_tokens, :system_prompt,
      settings: {},
      capabilities: [],
      agent_specializations_attributes: [:id, :name, :description, :domain, :expertise_level, :configuration, :_destroy]
    )
  end

  def calculate_avg_response_time(agent)
    # Implementation for average response time
    "2.5 min"
  end

  def calculate_satisfaction_rate(agent)
    # Implementation for satisfaction rate
    "94%"
  end

  def calculate_resolution_rate(agent)
    # Implementation for resolution rate
    "87%"
  end

  def generate_performance_data
    # Generate sample performance data for the chart
    {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      conversations: [250, 320, 280, 350, 410, 380, 420],
      resolved: [220, 290, 250, 310, 380, 350, 390]
    }
  end
end