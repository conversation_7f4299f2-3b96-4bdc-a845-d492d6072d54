class AgentsController < ApplicationController
  before_action :set_agent, only: [:show, :edit, :update, :destroy, :test, :conversations]

  def index
    ensure_current_tenant!
    @agents = current_tenant.agents.includes(:agent_specializations)
  end

  def show
    @recent_conversations = @agent.conversations
      .includes(:customer, :messages)
      .order(created_at: :desc)
      .limit(10)
    
    @performance_metrics = {
      total_conversations: @agent.conversations.count,
      avg_response_time: calculate_avg_response_time(@agent),
      satisfaction_rate: calculate_satisfaction_rate(@agent),
      resolution_rate: calculate_resolution_rate(@agent)
    }
  end

  def new
    ensure_current_tenant!
    return if performed? # Stop if ensure_current_tenant! redirected

    if current_tenant.nil?
      redirect_to root_path, alert: "Unable to determine current tenant. Please try again."
      return
    end

    @agent = current_tenant.agents.build

    # Ensure agent has required attributes for form
    @agent.name ||= ""
    @agent.kind ||= ""
    @agent.description ||= ""

    @agent.agent_specializations.build
  rescue => e
    Rails.logger.error "Error in agents#new: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    redirect_to root_path, alert: "An error occurred. Please try again."
  end

  def create
    ensure_current_tenant!
    @agent = current_tenant.agents.build(agent_params)

    if @agent.save
      redirect_to @agent, notice: 'Agent was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @agent.agent_specializations.build if @agent.agent_specializations.empty?
  end

  def update
    if @agent.update(agent_params)
      redirect_to @agent, notice: 'Agent was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @agent.destroy!
    redirect_to agents_url, notice: 'Agent was successfully deleted.'
  end

  def test
    test_message = params[:test_message] || "Hello, can you help me?"
    streaming = params[:streaming] == 'true'

    if streaming
      # Set up Server-Sent Events for streaming
      response.headers['Content-Type'] = 'text/event-stream'
      response.headers['Cache-Control'] = 'no-cache'
      response.headers['Connection'] = 'keep-alive'

      begin
        # Stream the response
        @agent.generate_response_stream([
          { role: 'system', content: build_test_system_prompt },
          { role: 'user', content: test_message }
        ]) do |chunk|
          render_stream_chunk(chunk)
        end

        # Send completion event
        render_stream_event('complete', {
          agent_name: @agent.name,
          message: 'Response completed'
        })
      rescue => e
        render_stream_event('error', { error: e.message })
      ensure
        response.stream.close
      end
    else
      # Standard non-streaming response
      response = Ai::TestService.new(@agent).test_response(test_message)

      render json: {
        success: true,
        response: response,
        agent_name: @agent.name,
        metadata: {
          model: @agent.model_name,
          temperature: @agent.temperature,
          max_tokens: @agent.max_tokens
        }
      }
    end
  rescue => e
    if streaming
      render_stream_event('error', { error: e.message })
      response.stream.close
    else
      render json: {
        success: false,
        error: e.message
      }, status: :unprocessable_entity
    end
  end

  def conversations
    @agents = current_tenant.agents
    @active_conversation = @agent.conversations.includes(:messages, :customer).last
    
    # Metrics for the right panel
    @metrics = {
      active_conversations: @agent.conversations.where(status: 'active').count,
      resolved_today: @agent.conversations.where(status: 'resolved', updated_at: Date.current.all_day).count,
      avg_response_time: "1.2s",
      satisfaction_score: 4.8
    }
    
    # Performance data for the chart
    @performance_data = generate_performance_data
    
    render layout: 'agent_conversation'
  end

  private

  def set_agent
    ensure_current_tenant!
    @agent = current_tenant.agents.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to agents_path, alert: "Agent not found."
  end

  def ensure_current_tenant!
    # Check if we already have a current tenant
    return if current_tenant.present?

    # Ensure we have a current user
    unless current_user
      redirect_to new_user_session_path, alert: "Please sign in first"
      return
    end

    # Try to set tenant from user's current_tenant
    if current_user.current_tenant.present?
      Current.tenant = current_user.current_tenant
      ActsAsTenant.current_tenant = current_user.current_tenant
      Rails.logger.info "Set current tenant from user: #{current_user.current_tenant.name}"
      return
    end

    # If user has tenants but no current_tenant set, use the first one
    user_tenants = current_user.tenants
    if user_tenants.any?
      tenant = user_tenants.first
      current_user.update!(current_tenant: tenant)
      Current.tenant = tenant
      ActsAsTenant.current_tenant = tenant
      Rails.logger.info "Set current tenant to first available: #{tenant.name}"
      return
    end

    # No tenant available, redirect to dashboard with instructions
    redirect_to root_path, alert: "Please create or select a tenant first to manage agents"
  end

  def agent_params
    params.require(:agent).permit(
      :name, :kind, :description, :status, :llm_provider,
      :role, :personality, :greeting_message, :avatar_url, :response_style, :language,
      :model_provider, :ai_model_name, :temperature, :max_tokens, :system_prompt,
      settings: {},
      capabilities: [],
      agent_specializations_attributes: [:id, :name, :description, :domain, :expertise_level, :configuration, :_destroy]
    )
  end

  def calculate_avg_response_time(agent)
    # Calculate actual average response time from conversations
    conversations = agent.conversations.includes(:messages)
                        .where(created_at: 30.days.ago..Time.current)

    return "0 min" if conversations.empty?

    total_response_time = 0
    response_count = 0

    conversations.each do |conversation|
      messages = conversation.messages.order(:created_at)

      messages.each_cons(2) do |user_msg, agent_msg|
        if user_msg.sender_type == 'Customer' && agent_msg.sender_type == 'Agent'
          response_time = agent_msg.created_at - user_msg.created_at
          total_response_time += response_time
          response_count += 1
        end
      end
    end

    return "0 min" if response_count.zero?

    avg_seconds = total_response_time / response_count
    if avg_seconds < 60
      "#{avg_seconds.round}s"
    elsif avg_seconds < 3600
      "#{(avg_seconds / 60).round(1)} min"
    else
      "#{(avg_seconds / 3600).round(1)} hr"
    end
  end

  def calculate_satisfaction_rate(agent)
    # Calculate satisfaction from conversation insights
    insights = ConversationInsight.joins(:conversation)
                                 .where(conversations: { assigned_agent: agent })
                                 .where(created_at: 30.days.ago..Time.current)

    return "N/A" if insights.empty?

    positive_sentiments = insights.where('sentiment > ?', 0.1).count
    total_insights = insights.count

    satisfaction_rate = (positive_sentiments.to_f / total_insights * 100).round
    "#{satisfaction_rate}%"
  end

  def calculate_resolution_rate(agent)
    # Calculate resolution rate from conversation status
    total_conversations = agent.conversations
                              .where(created_at: 30.days.ago..Time.current)
                              .count

    return "N/A" if total_conversations.zero?

    resolved_conversations = agent.conversations
                                 .where(status: 'resolved')
                                 .where(created_at: 30.days.ago..Time.current)
                                 .count

    resolution_rate = (resolved_conversations.to_f / total_conversations * 100).round
    "#{resolution_rate}%"
  end

  def generate_performance_data
    # Generate real performance data for the chart
    end_date = Date.current
    start_date = end_date - 6.days

    labels = []
    conversations_data = []
    resolved_data = []

    (start_date..end_date).each do |date|
      labels << date.strftime('%a')

      daily_conversations = @agent.conversations
                                  .where(created_at: date.beginning_of_day..date.end_of_day)
                                  .count

      daily_resolved = @agent.conversations
                             .where(status: 'resolved')
                             .where(updated_at: date.beginning_of_day..date.end_of_day)
                             .count

      conversations_data << daily_conversations
      resolved_data << daily_resolved
    end

    {
      labels: labels,
      conversations: conversations_data,
      resolved: resolved_data
    }
  end

  def render_stream_chunk(chunk)
    response.stream.write("data: #{chunk.to_json}\n\n")
  end

  def render_stream_event(event, data)
    response.stream.write("event: #{event}\n")
    response.stream.write("data: #{data.to_json}\n\n")
  end

  def build_test_system_prompt
    base_prompt = "You are #{@agent.name}, a #{@agent.kind.humanize.downcase} AI assistant."

    if @agent.system_prompt.present?
      base_prompt += " #{@agent.system_prompt}"
    end

    if @agent.capabilities.present?
      base_prompt += " Your capabilities include: #{@agent.capabilities.join(', ')}."
    end

    base_prompt += " This is a test conversation to evaluate your responses."
    base_prompt
  end
end