class Agent < ApplicationRecord
  include TenantScoped
  
  # Associations
  has_many :agent_specializations, dependent: :destroy
  has_many :messages
  has_many :conversations, foreign_key: 'assigned_agent_id'
  has_many :agent_handoffs_from, class_name: '<PERSON><PERSON><PERSON><PERSON>', foreign_key: 'from_agent_id'
  has_many :agent_handoffs_to, class_name: '<PERSON><PERSON><PERSON><PERSON>', foreign_key: 'to_agent_id'
  
  # Validations
  validates :name, presence: true
  validates :kind, presence: true
  validates :llm_provider, inclusion: { in: %w[openai anthropic google deepseek openrouter ollama bedrock] }, allow_nil: true
  
  # Status enum
  enum :status, {
    active: 0,
    inactive: 1,
    training: 2,
    maintenance: 3
  }, default: :active
  
  # Agent types
  AGENT_KINDS = %w[
    customer_service
    sales
    technical_support
    hr_operations
    marketing
    analytics
    predictive
    autonomous
  ].freeze
  
  validates :kind, inclusion: { in: AGENT_KINDS }
  
  # Scopes
  scope :active, -> { where(status: :active) }
  scope :by_kind, ->(kind) { where(kind: kind) }
  
  # Default settings
  after_initialize :set_default_settings

  # Virtual attributes for form fields that are stored in settings
  %w[role personality greeting_message avatar_url response_style language
     model_provider model_name temperature max_tokens system_prompt llm_provider].each do |attr|
    define_method(attr) do
      settings&.dig(attr) || ''
    end

    define_method("#{attr}=") do |value|
      self.settings ||= {}
      self.settings[attr] = value
    end
  end

  # Accept nested attributes for specializations
  accepts_nested_attributes_for :agent_specializations, allow_destroy: true, reject_if: :all_blank
  
  # Methods
  def available?
    active? && within_capacity?
  end
  
  def within_capacity?
    # Check if agent is within conversation capacity
    current_conversations = Message.joins(:conversation)
                                  .where(sender: self)
                                  .where(conversations: { status: :active })
                                  .distinct.count(:conversation_id)
    
    max_capacity = settings.dig('max_concurrent_conversations') || 10
    current_conversations < max_capacity
  end
  
  def specialization_for(domain)
    agent_specializations.find_by(domain: domain)
  end
  
  def ai_service
    @ai_service ||= AiService.new(tenant: tenant, agent: self)
  end
  
  def generate_response(messages, **options)
    ai_service.chat(messages, **options)
  end
  
  def generate_response_stream(messages, **options, &block)
    ai_service.stream_chat(messages, **options, &block)
  end
  
  # Dashboard metrics helpers
  def avg_response_time
    "0.85s"
  end
  
  def uptime
    "99.8%"
  end
  
  def llm_provider
    settings&.dig('llm_provider') || 'openai'
  end
  
  private
  
  def set_default_settings
    self.settings ||= {
      'max_concurrent_conversations' => 10,
      'response_style' => 'professional',
      'language_capabilities' => ['en'],
      'escalation_threshold' => 0.3,
      'temperature' => 0.7,
      'max_tokens' => 1000
    }
    
    self.capabilities ||= default_capabilities_for_kind
  end
  
  def default_capabilities_for_kind
    case kind
    when 'customer_service'
      ['order_inquiry', 'product_questions', 'return_processing', 'complaint_handling']
    when 'sales'
      ['product_recommendation', 'pricing_negotiation', 'upselling', 'lead_qualification']
    when 'technical_support'
      ['troubleshooting', 'bug_reporting', 'integration_help', 'api_support']
    else
      ['general_inquiry']
    end
  end
end
